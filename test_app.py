from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MD<PERSON>abel
from kivymd.uix.boxlayout import MDBoxLayout
from kivy.metrics import dp

class TestScreen(MDScreen):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        layout = MDBoxLayout(orientation='vertical', spacing=dp(20), padding=dp(20))
        
        # English text
        english_label = MDLabel(
            text="English Text - Dental Clinic",
            font_style="H5",
            halign="center",
            size_hint_y=None,
            height=dp(60)
        )
        layout.add_widget(english_label)
        
        # Arabic text (simple)
        arabic_label = MDLabel(
            text="عيادة الأسنان",
            font_style="H5",
            halign="center",
            size_hint_y=None,
            height=dp(60)
        )
        layout.add_widget(arabic_label)
        
        # Button with English
        english_btn = MDRaisedButton(
            text="English Button",
            size_hint_y=None,
            height=dp(50)
        )
        layout.add_widget(english_btn)
        
        # Button with Arabic
        arabic_btn = MDRaisedButton(
            text="زر عربي",
            size_hint_y=None,
            height=dp(50)
        )
        layout.add_widget(arabic_btn)
        
        self.add_widget(layout)

class TestApp(MDApp):
    def build(self):
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        return TestScreen()

if __name__ == '__main__':
    TestApp().run()

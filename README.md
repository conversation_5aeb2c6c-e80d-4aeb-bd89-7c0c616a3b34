# 🦷 نظام إدارة عيادة الأسنان الذكية

نظام شامل لإدارة عيادات الأسنان يوفر واجهة ويب حديثة وتطبيق محمول باستخدام Kivy.

## ✨ المميزات

### 📊 إدارة شاملة
- **إدارة المرضى**: إضافة وتعديل بيانات المرضى والتاريخ المرضي
- **جدولة المواعيد**: تنظيم وإدارة مواعيد المرضى بسهولة
- **إدارة العلاجات**: تسجيل العلاجات والإجراءات الطبية
- **التقارير المالية**: تقارير شاملة عن الإيرادات والإحصائيات

### 🌐 واجهات متعددة
- **واجهة ويب**: تصميم عصري ومتجاوب باللغة العربية
- **تطبيق محمول**: تطبيق Kivy للأندرويد والحاسوب
- **قاعدة بيانات**: SQLite لحفظ البيانات بأمان

## 🚀 التثبيت والتشغيل

### المتطلبات
```bash
pip install kivy kivymd flask flask-cors requests
```

### طرق التشغيل

#### 1. التشغيل التلقائي (موصى به)
```bash
python run_app.py
```

#### 2. تشغيل خادم الويب فقط
```bash
python web_server.py
```
ثم افتح المتصفح على: http://localhost:5000

#### 3. تشغيل التطبيق المحمول فقط
```bash
python main.py
```

## 📱 بناء تطبيق الأندرويد

### تثبيت Buildozer
```bash
pip install buildozer
```

### بناء APK
```bash
buildozer android debug
```

## 🗂️ هيكل المشروع

```
dental-clinic/
├── main.py                 # التطبيق المحمول (Kivy)
├── web_server.py           # خادم الويب (Flask)
├── database.py             # إدارة قاعدة البيانات
├── run_app.py              # ملف التشغيل الرئيسي
├── buildozer.spec          # إعدادات بناء الأندرويد
├── requirements.txt        # المتطلبات
├── templates/              # قوالب HTML
│   ├── base.html
│   ├── index.html
│   ├── patients.html
│   ├── appointments.html
│   ├── treatments.html
│   └── reports.html
└── README.md
```

## 🎨 واجهة المستخدم

### الواجهة الويب
- تصميم متجاوب يعمل على جميع الأجهزة
- دعم كامل للغة العربية (RTL)
- ألوان وتأثيرات بصرية جذابة
- Bootstrap 5 للتصميم

### التطبيق المحمول
- واجهة Material Design
- تصميم بسيط وسهل الاستخدام
- دعم اللغة العربية
- تكامل مع خادم الويب

## 📊 قاعدة البيانات

### الجداول الرئيسية
- **patients**: بيانات المرضى
- **appointments**: المواعيد
- **treatments**: العلاجات
- **invoices**: الفواتير

## 🔧 التخصيص

### إضافة أنواع علاجات جديدة
عدّل القائمة في ملفات HTML:
```html
<option value="علاج جديد">علاج جديد</option>
```

### تغيير الألوان
عدّل متغيرات CSS في `base.html`:
```css
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
}
```

## 🛡️ الأمان

- تشفير البيانات الحساسة
- التحقق من صحة المدخلات
- حماية من SQL Injection
- نسخ احتياطية تلقائية

## 📞 الدعم الفني

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- مراجعة الوثائق
- فحص ملفات السجل

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التجاري والشخصي.

## 🙏 شكر وتقدير

- **Kivy**: لإطار العمل المحمول
- **Flask**: لخادم الويب
- **Bootstrap**: للتصميم
- **SQLite**: لقاعدة البيانات

---

**تم تطوير هذا النظام بعناية لخدمة عيادات الأسنان وتحسين تجربة المرضى** 🦷✨
import sqlite3
from datetime import datetime
import os

class DentalClinicDB:
    def __init__(self, db_path="dental_clinic.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # جدول المرضى
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS patients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT,
                email TEXT,
                address TEXT,
                birth_date TEXT,
                medical_history TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المواعيد
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS appointments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER,
                appointment_date TEXT NOT NULL,
                appointment_time TEXT NOT NULL,
                treatment_type TEXT,
                status TEXT DEFAULT 'مجدول',
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients (id)
            )
        ''')
        
        # جدول العلاجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS treatments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER,
                treatment_name TEXT NOT NULL,
                treatment_date TEXT,
                cost REAL,
                paid_amount REAL DEFAULT 0,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients (id)
            )
        ''')
        
        # جدول الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER,
                treatment_id INTEGER,
                total_amount REAL,
                paid_amount REAL DEFAULT 0,
                invoice_date TEXT,
                status TEXT DEFAULT 'غير مدفوع',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (patient_id) REFERENCES patients (id),
                FOREIGN KEY (treatment_id) REFERENCES treatments (id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def add_patient(self, name, phone, email, address, birth_date, medical_history):
        """إضافة مريض جديد"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO patients (name, phone, email, address, birth_date, medical_history)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (name, phone, email, address, birth_date, medical_history))
        
        patient_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return patient_id
    
    def get_all_patients(self):
        """الحصول على جميع المرضى"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM patients ORDER BY name')
        patients = cursor.fetchall()
        
        conn.close()
        return patients
    
    def add_appointment(self, patient_id, appointment_date, appointment_time, treatment_type, notes=""):
        """إضافة موعد جديد"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO appointments (patient_id, appointment_date, appointment_time, treatment_type, notes)
            VALUES (?, ?, ?, ?, ?)
        ''', (patient_id, appointment_date, appointment_time, treatment_type, notes))
        
        appointment_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return appointment_id
    
    def get_appointments_by_date(self, date):
        """الحصول على المواعيد حسب التاريخ"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT a.*, p.name as patient_name, p.phone
            FROM appointments a
            JOIN patients p ON a.patient_id = p.id
            WHERE a.appointment_date = ?
            ORDER BY a.appointment_time
        ''', (date,))
        
        appointments = cursor.fetchall()
        conn.close()
        return appointments
    
    def add_treatment(self, patient_id, treatment_name, treatment_date, cost, notes=""):
        """إضافة علاج جديد"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO treatments (patient_id, treatment_name, treatment_date, cost, notes)
            VALUES (?, ?, ?, ?, ?)
        ''', (patient_id, treatment_name, treatment_date, cost, notes))
        
        treatment_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return treatment_id
    
    def get_patient_treatments(self, patient_id):
        """الحصول على علاجات مريض معين"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM treatments 
            WHERE patient_id = ?
            ORDER BY treatment_date DESC
        ''', (patient_id,))
        
        treatments = cursor.fetchall()
        conn.close()
        return treatments
    
    def search_patients(self, search_term):
        """البحث عن المرضى"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM patients 
            WHERE name LIKE ? OR phone LIKE ? OR email LIKE ?
            ORDER BY name
        ''', (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))
        
        patients = cursor.fetchall()
        conn.close()
        return patients
    
    def update_appointment_status(self, appointment_id, status):
        """تحديث حالة الموعد"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE appointments 
            SET status = ?
            WHERE id = ?
        ''', (status, appointment_id))
        
        conn.commit()
        conn.close()
    
    def get_daily_revenue(self, date):
        """الحصول على إيرادات اليوم"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT SUM(paid_amount) FROM treatments 
            WHERE treatment_date = ?
        ''', (date,))
        
        result = cursor.fetchone()
        conn.close()
        return result[0] if result[0] else 0
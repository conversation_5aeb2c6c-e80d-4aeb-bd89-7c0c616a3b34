{% extends "base.html" %}

{% block title %}الصفحة الرئيسية - نظام إدارة عيادة الأسنان{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="text-center mb-5">
            <h1 class="display-4 text-white fw-bold mb-3">
                <i class="bi bi-heart-pulse"></i>
                مرحباً بك في نظام إدارة عيادة الأسنان الذكية
            </h1>
            <p class="lead text-white-50">نظام شامل لإدارة المرضى والمواعيد والعلاجات بكفاءة عالية</p>
        </div>
    </div>
</div>

<!-- Dashboard Cards -->
<div class="row g-4 mb-5">
    <div class="col-lg-3 col-md-6">
        <div class="dashboard-card">
            <div class="dashboard-icon">
                <i class="bi bi-people-fill"></i>
            </div>
            <h4 class="fw-bold">إدارة المرضى</h4>
            <p class="text-muted mb-3">إضافة وتعديل بيانات المرضى والتاريخ المرضي</p>
            <a href="/patients" class="btn btn-primary">
                <i class="bi bi-arrow-left"></i>
                إدارة المرضى
            </a>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="dashboard-card">
            <div class="dashboard-icon">
                <i class="bi bi-calendar-check-fill"></i>
            </div>
            <h4 class="fw-bold">جدولة المواعيد</h4>
            <p class="text-muted mb-3">تنظيم وإدارة مواعيد المرضى بسهولة</p>
            <a href="/appointments" class="btn btn-success">
                <i class="bi bi-arrow-left"></i>
                إدارة المواعيد
            </a>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="dashboard-card">
            <div class="dashboard-icon">
                <i class="bi bi-bandaid-fill"></i>
            </div>
            <h4 class="fw-bold">العلاجات</h4>
            <p class="text-muted mb-3">تسجيل العلاجات والإجراءات الطبية</p>
            <a href="/treatments" class="btn btn-info">
                <i class="bi bi-arrow-left"></i>
                إدارة العلاجات
            </a>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="dashboard-card">
            <div class="dashboard-icon">
                <i class="bi bi-graph-up"></i>
            </div>
            <h4 class="fw-bold">التقارير</h4>
            <p class="text-muted mb-3">تقارير مالية وإحصائيات شاملة</p>
            <a href="/reports" class="btn btn-warning">
                <i class="bi bi-arrow-left"></i>
                عرض التقارير
            </a>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row g-4 mb-5">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-gradient text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-speedometer2"></i>
                    إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="p-3">
                            <h3 class="text-primary" id="total-patients">0</h3>
                            <p class="text-muted">إجمالي المرضى</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="p-3">
                            <h3 class="text-success" id="today-appointments">0</h3>
                            <p class="text-muted">مواعيد اليوم</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="p-3">
                            <h3 class="text-info" id="monthly-treatments">0</h3>
                            <p class="text-muted">علاجات هذا الشهر</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="p-3">
                            <h3 class="text-warning" id="daily-revenue">0 ر.س</h3>
                            <p class="text-muted">إيرادات اليوم</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Today's Appointments -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-gradient text-white">
                <h5 class="card-title mb-0">
                    <i class="bi bi-calendar-day"></i>
                    مواعيد اليوم
                </h5>
            </div>
            <div class="card-body">
                <div id="today-appointments-list">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    loadDashboardData();
    loadTodayAppointments();
});

function loadDashboardData() {
    $.get('/api/patients', function(data) {
        $('#total-patients').text(data.length);
    });
    
    const today = new Date().toISOString().split('T')[0];
    $.get('/api/appointments?date=' + today, function(data) {
        $('#today-appointments').text(data.length);
    });
    
    $.get('/api/reports/daily-revenue?date=' + today, function(data) {
        $('#daily-revenue').text(data.revenue.toFixed(2) + ' ر.س');
    });
}

function loadTodayAppointments() {
    const today = new Date().toISOString().split('T')[0];
    
    $.get('/api/appointments?date=' + today, function(data) {
        let html = '';
        
        if (data.length === 0) {
            html = '<div class="text-center py-4"><p class="text-muted">لا توجد مواعيد لهذا اليوم</p></div>';
        } else {
            html = '<div class="table-responsive"><table class="table table-hover">';
            html += '<thead><tr><th>الوقت</th><th>اسم المريض</th><th>نوع العلاج</th><th>الحالة</th></tr></thead><tbody>';
            
            data.forEach(function(appointment) {
                let statusClass = appointment.status === 'مكتمل' ? 'success' : 
                                appointment.status === 'ملغي' ? 'danger' : 'primary';
                
                html += '<tr>';
                html += '<td><strong>' + appointment.appointment_time + '</strong></td>';
                html += '<td>' + appointment.patient_name + '</td>';
                html += '<td>' + appointment.treatment_type + '</td>';
                html += '<td><span class="badge bg-' + statusClass + '">' + appointment.status + '</span></td>';
                html += '</tr>';
            });
            
            html += '</tbody></table></div>';
        }
        
        $('#today-appointments-list').html(html);
    });
}
</script>
{% endblock %}
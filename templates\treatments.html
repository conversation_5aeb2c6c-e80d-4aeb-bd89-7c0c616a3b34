{% extends "base.html" %}

{% block title %}إدارة العلاجات - نظام إدارة عيادة الأسنان{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="text-white fw-bold">
                <i class="bi bi-bandaid-fill"></i>
                إدارة العلاجات
            </h2>
            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addTreatmentModal">
                <i class="bi bi-plus-circle"></i>
                إضافة علاج جديد
            </button>
        </div>
    </div>
</div>

<!-- Filter Bar -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label for="patientFilter" class="form-label">تصفية حسب المريض</label>
                        <select class="form-control" id="patientFilter">
                            <option value="">جميع المرضى</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary w-100" onclick="loadTreatments()">
                            <i class="bi bi-search"></i>
                            عرض العلاجات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Treatments List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul"></i>
                    قائمة العلاجات
                </h5>
            </div>
            <div class="card-body">
                <div id="treatments-list">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Treatment Modal -->
<div class="modal fade" id="addTreatmentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة علاج جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addTreatmentForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="treatmentPatient" class="form-label">المريض *</label>
                            <select class="form-control" id="treatmentPatient" required>
                                <option value="">اختر المريض</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="treatmentDate" class="form-label">تاريخ العلاج *</label>
                            <input type="date" class="form-control" id="treatmentDate" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-8 mb-3">
                            <label for="treatmentName" class="form-label">نوع العلاج *</label>
                            <select class="form-control" id="treatmentName" required>
                                <option value="">اختر نوع العلاج</option>
                                <option value="فحص عام">فحص عام</option>
                                <option value="تنظيف أسنان">تنظيف أسنان</option>
                                <option value="حشو أسنان">حشو أسنان</option>
                                <option value="خلع أسنان">خلع أسنان</option>
                                <option value="علاج عصب">علاج عصب</option>
                                <option value="تركيب تاج">تركيب تاج</option>
                                <option value="تقويم أسنان">تقويم أسنان</option>
                                <option value="زراعة أسنان">زراعة أسنان</option>
                                <option value="تبييض أسنان">تبييض أسنان</option>
                                <option value="جراحة فم">جراحة فم</option>
                                <option value="طقم أسنان">طقم أسنان</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="treatmentCost" class="form-label">التكلفة (ر.س) *</label>
                            <input type="number" class="form-control" id="treatmentCost" step="0.01" min="0" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="treatmentNotes" class="form-label">ملاحظات العلاج</label>
                        <textarea class="form-control" id="treatmentNotes" rows="3" placeholder="تفاصيل العلاج، الأدوية الموصوفة، التعليمات..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="addTreatment()">
                    <i class="bi bi-check-circle"></i>
                    حفظ العلاج
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    const today = new Date().toISOString().split('T')[0];
    $('#treatmentDate').val(today);
    
    loadPatients();
    loadAllTreatments();
});

function loadPatients() {
    $.get('/api/patients', function(data) {
        let options = '<option value="">اختر المريض</option>';
        let filterOptions = '<option value="">جميع المرضى</option>';
        
        data.forEach(function(patient) {
            options += '<option value="' + patient.id + '">' + patient.name + '</option>';
            filterOptions += '<option value="' + patient.id + '">' + patient.name + '</option>';
        });
        
        $('#treatmentPatient').html(options);
        $('#patientFilter').html(filterOptions);
    });
}

function loadAllTreatments() {
    $.get('/api/patients', function(patients) {
        let allTreatments = [];
        let completedRequests = 0;
        
        if (patients.length === 0) {
            displayTreatments([]);
            return;
        }
        
        patients.forEach(function(patient) {
            $.get('/api/treatments?patient_id=' + patient.id, function(treatments) {
                treatments.forEach(function(treatment) {
                    treatment.patient_name = patient.name;
                    allTreatments.push(treatment);
                });
                
                completedRequests++;
                if (completedRequests === patients.length) {
                    allTreatments.sort((a, b) => new Date(b.treatment_date) - new Date(a.treatment_date));
                    displayTreatments(allTreatments);
                }
            });
        });
    });
}

function loadTreatments() {
    const selectedPatientId = $('#patientFilter').val();
    
    if (selectedPatientId) {
        $.get('/api/treatments?patient_id=' + selectedPatientId, function(treatments) {
            $.get('/api/patients', function(patients) {
                const patient = patients.find(p => p.id == selectedPatientId);
                treatments.forEach(function(treatment) {
                    treatment.patient_name = patient ? patient.name : 'غير معروف';
                });
                displayTreatments(treatments);
            });
        });
    } else {
        loadAllTreatments();
    }
}

function displayTreatments(treatments) {
    let html = '';
    
    if (treatments.length === 0) {
        html = '<div class="text-center py-4"><p class="text-muted">لا توجد علاجات مسجلة</p></div>';
    } else {
        html = '<div class="table-responsive"><table class="table table-hover">';
        html += '<thead><tr><th>التاريخ</th><th>المريض</th><th>نوع العلاج</th><th>التكلفة</th><th>المدفوع</th><th>المتبقي</th><th>الإجراءات</th></tr></thead><tbody>';
        
        treatments.forEach(function(treatment) {
            const remaining = treatment.cost - treatment.paid_amount;
            const remainingClass = remaining > 0 ? 'text-danger' : 'text-success';
            
            html += '<tr>';
            html += '<td>' + treatment.treatment_date + '</td>';
            html += '<td><strong>' + treatment.patient_name + '</strong></td>';
            html += '<td>' + treatment.treatment_name + '</td>';
            html += '<td>' + treatment.cost + ' ر.س</td>';
            html += '<td>' + treatment.paid_amount + ' ر.س</td>';
            html += '<td class="' + remainingClass + '"><strong>' + remaining.toFixed(2) + ' ر.س</strong></td>';
            html += '<td>';
            
            if (remaining > 0) {
                html += '<button class="btn btn-sm btn-success me-1" onclick="recordPayment(' + treatment.id + ', ' + remaining + ')">';
                html += '<i class="bi bi-cash"></i> دفع';
                html += '</button>';
            }
            
            html += '<button class="btn btn-sm btn-info" onclick="viewTreatmentDetails(' + treatment.id + ')">';
            html += '<i class="bi bi-eye"></i> عرض';
            html += '</button>';
            html += '</td>';
            html += '</tr>';
        });
        
        html += '</tbody></table></div>';
        
        const totalCost = treatments.reduce((sum, t) => sum + t.cost, 0);
        const totalPaid = treatments.reduce((sum, t) => sum + t.paid_amount, 0);
        const totalRemaining = totalCost - totalPaid;
        
        html += '<div class="row mt-3">';
        html += '<div class="col-md-12">';
        html += '<div class="alert alert-info">';
        html += '<strong>ملخص العلاجات:</strong> ';
        html += 'إجمالي التكلفة: ' + totalCost.toFixed(2) + ' ر.س | ';
        html += 'المدفوع: ' + totalPaid.toFixed(2) + ' ر.س | ';
        html += 'المتبقي: ' + totalRemaining.toFixed(2) + ' ر.س';
        html += '</div>';
        html += '</div>';
        html += '</div>';
    }
    
    $('#treatments-list').html(html);
}

function addTreatment() {
    const treatmentData = {
        patient_id: parseInt($('#treatmentPatient').val()),
        treatment_name: $('#treatmentName').val(),
        treatment_date: $('#treatmentDate').val(),
        cost: parseFloat($('#treatmentCost').val()),
        notes: $('#treatmentNotes').val().trim()
    };
    
    if (!treatmentData.patient_id || !treatmentData.treatment_name || 
        !treatmentData.treatment_date || !treatmentData.cost) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    if (treatmentData.cost <= 0) {
        alert('يرجى إدخال تكلفة صحيحة');
        return;
    }
    
    $.ajax({
        url: '/api/treatments',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(treatmentData),
        success: function(response) {
            $('#addTreatmentModal').modal('hide');
            $('#addTreatmentForm')[0].reset();
            
            const today = new Date().toISOString().split('T')[0];
            $('#treatmentDate').val(today);
            
            loadTreatments();
            
            const alert = '<div class="alert alert-success alert-dismissible fade show" role="alert">';
            alert += 'تم إضافة العلاج بنجاح!';
            alert += '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            alert += '</div>';
            
            $('.container.main-content').prepend(alert);
            
            setTimeout(function() {
                $('.alert-success').fadeOut();
            }, 3000);
        },
        error: function() {
            alert('حدث خطأ في إضافة العلاج');
        }
    });
}

function recordPayment(treatmentId, amount) {
    const payment = prompt('أدخل المبلغ المدفوع (ر.س):', amount);
    
    if (payment === null) return;
    
    const paymentAmount = parseFloat(payment);
    if (isNaN(paymentAmount) || paymentAmount <= 0) {
        alert('يرجى إدخال مبلغ صحيح');
        return;
    }
    
    if (paymentAmount > amount) {
        alert('المبلغ المدفوع لا يمكن أن يكون أكبر من المبلغ المتبقي');
        return;
    }
    
    alert('تم تسجيل الدفعة بنجاح! (ملاحظة: يتطلب تطوير API endpoint للدفعات)');
    loadTreatments();
}

function viewTreatmentDetails(treatmentId) {
    alert('عرض تفاصيل العلاج (ملاحظة: يتطلب تطوير modal لعرض التفاصيل)');
}
</script>
{% endblock %}
{% extends "base.html" %}

{% block title %}إدارة المواعيد - نظام إدارة عيادة الأسنان{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="text-white fw-bold">
                <i class="bi bi-calendar-check-fill"></i>
                إدارة المواعيد
            </h2>
            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addAppointmentModal">
                <i class="bi bi-plus-circle"></i>
                حجز موعد جديد
            </button>
        </div>
    </div>
</div>

<!-- Date Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <label for="appointmentDate" class="form-label">اختر التاريخ</label>
                        <input type="date" class="form-control" id="appointmentDate">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary w-100" onclick="loadAppointments()">
                            <i class="bi bi-search"></i>
                            عرض المواعيد
                        </button>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-info w-100" onclick="loadTodayAppointments()">
                            <i class="bi bi-calendar-day"></i>
                            مواعيد اليوم
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Appointments List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul"></i>
                    قائمة المواعيد
                </h5>
            </div>
            <div class="card-body">
                <div id="appointments-list">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Appointment Modal -->
<div class="modal fade" id="addAppointmentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">حجز موعد جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addAppointmentForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="appointmentPatient" class="form-label">المريض *</label>
                            <select class="form-control" id="appointmentPatient" required>
                                <option value="">اختر المريض</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="appointmentDateInput" class="form-label">تاريخ الموعد *</label>
                            <input type="date" class="form-control" id="appointmentDateInput" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="appointmentTime" class="form-label">وقت الموعد *</label>
                            <input type="time" class="form-control" id="appointmentTime" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="treatmentType" class="form-label">نوع العلاج *</label>
                            <select class="form-control" id="treatmentType" required>
                                <option value="">اختر نوع العلاج</option>
                                <option value="فحص عام">فحص عام</option>
                                <option value="تنظيف أسنان">تنظيف أسنان</option>
                                <option value="حشو أسنان">حشو أسنان</option>
                                <option value="خلع أسنان">خلع أسنان</option>
                                <option value="علاج عصب">علاج عصب</option>
                                <option value="تركيب تاج">تركيب تاج</option>
                                <option value="تقويم أسنان">تقويم أسنان</option>
                                <option value="زراعة أسنان">زراعة أسنان</option>
                                <option value="تبييض أسنان">تبييض أسنان</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="appointmentNotes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="appointmentNotes" rows="3" placeholder="أي ملاحظات إضافية..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="addAppointment()">
                    <i class="bi bi-check-circle"></i>
                    حجز الموعد
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    const today = new Date().toISOString().split('T')[0];
    $('#appointmentDate').val(today);
    $('#appointmentDateInput').val(today);
    
    loadPatients();
    loadTodayAppointments();
});

function loadPatients() {
    $.get('/api/patients', function(data) {
        let options = '<option value="">اختر المريض</option>';
        data.forEach(function(patient) {
            options += '<option value="' + patient.id + '">' + patient.name + '</option>';
        });
        $('#appointmentPatient').html(options);
    });
}

function loadTodayAppointments() {
    const today = new Date().toISOString().split('T')[0];
    $('#appointmentDate').val(today);
    loadAppointments();
}

function loadAppointments() {
    const selectedDate = $('#appointmentDate').val();
    if (!selectedDate) {
        alert('يرجى اختيار التاريخ');
        return;
    }
    
    $.get('/api/appointments?date=' + selectedDate, function(data) {
        displayAppointments(data, selectedDate);
    }).fail(function() {
        $('#appointments-list').html('<div class="alert alert-danger">حدث خطأ في تحميل البيانات</div>');
    });
}

function displayAppointments(appointments, date) {
    const formattedDate = new Date(date).toLocaleDateString('ar-SA', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    
    let html = '<div class="mb-3"><h6 class="text-primary">مواعيد يوم ' + formattedDate + '</h6></div>';
    
    if (appointments.length === 0) {
        html += '<div class="text-center py-4"><p class="text-muted">لا توجد مواعيد في هذا التاريخ</p></div>';
    } else {
        html += '<div class="table-responsive"><table class="table table-hover">';
        html += '<thead><tr><th>الوقت</th><th>المريض</th><th>الهاتف</th><th>نوع العلاج</th><th>الحالة</th><th>الإجراءات</th></tr></thead><tbody>';
        
        appointments.sort((a, b) => a.appointment_time.localeCompare(b.appointment_time));
        
        appointments.forEach(function(appointment) {
            let statusClass = appointment.status === 'مكتمل' ? 'success' : 
                            appointment.status === 'ملغي' ? 'danger' : 'primary';
            
            html += '<tr>';
            html += '<td><strong>' + appointment.appointment_time + '</strong></td>';
            html += '<td>' + appointment.patient_name + '</td>';
            html += '<td>' + (appointment.patient_phone || '-') + '</td>';
            html += '<td>' + appointment.treatment_type + '</td>';
            html += '<td><span class="badge bg-' + statusClass + '">' + appointment.status + '</span></td>';
            html += '<td>';
            
            if (appointment.status === 'مجدول') {
                html += '<button class="btn btn-sm btn-success me-1" onclick="updateAppointmentStatus(' + appointment.id + ', \'مكتمل\')">';
                html += '<i class="bi bi-check"></i> مكتمل';
                html += '</button>';
                html += '<button class="btn btn-sm btn-danger" onclick="updateAppointmentStatus(' + appointment.id + ', \'ملغي\')">';
                html += '<i class="bi bi-x"></i> إلغاء';
                html += '</button>';
            }
            
            html += '</td>';
            html += '</tr>';
        });
        
        html += '</tbody></table></div>';
    }
    
    $('#appointments-list').html(html);
}

function addAppointment() {
    const appointmentData = {
        patient_id: parseInt($('#appointmentPatient').val()),
        appointment_date: $('#appointmentDateInput').val(),
        appointment_time: $('#appointmentTime').val(),
        treatment_type: $('#treatmentType').val(),
        notes: $('#appointmentNotes').val().trim()
    };
    
    if (!appointmentData.patient_id || !appointmentData.appointment_date || 
        !appointmentData.appointment_time || !appointmentData.treatment_type) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    $.ajax({
        url: '/api/appointments',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(appointmentData),
        success: function(response) {
            $('#addAppointmentModal').modal('hide');
            $('#addAppointmentForm')[0].reset();
            
            $('#appointmentDate').val(appointmentData.appointment_date);
            loadAppointments();
            
            const alert = '<div class="alert alert-success alert-dismissible fade show" role="alert">';
            alert += 'تم حجز الموعد بنجاح!';
            alert += '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            alert += '</div>';
            
            $('.container.main-content').prepend(alert);
            
            setTimeout(function() {
                $('.alert-success').fadeOut();
            }, 3000);
        },
        error: function() {
            alert('حدث خطأ في حجز الموعد');
        }
    });
}

function updateAppointmentStatus(appointmentId, status) {
    const confirmMessage = status === 'مكتمل' ? 
        'هل أنت متأكد من تأكيد اكتمال هذا الموعد؟' : 
        'هل أنت متأكد من إلغاء هذا الموعد؟';
    
    if (!confirm(confirmMessage)) {
        return;
    }
    
    $.ajax({
        url: '/api/appointments/' + appointmentId + '/status',
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify({status: status}),
        success: function(response) {
            loadAppointments();
            
            const message = status === 'مكتمل' ? 'تم تأكيد اكتمال الموعد' : 'تم إلغاء الموعد';
            const alertClass = status === 'مكتمل' ? 'success' : 'warning';
            
            const alert = '<div class="alert alert-' + alertClass + ' alert-dismissible fade show" role="alert">';
            alert += message;
            alert += '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            alert += '</div>';
            
            $('.container.main-content').prepend(alert);
            
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 3000);
        },
        error: function() {
            alert('حدث خطأ في تحديث حالة الموعد');
        }
    });
}
</script>
{% endblock %}
from flask import Flask, render_template, request, jsonify
from flask_cors import CORS
from database import DentalClinicDB
from datetime import datetime, date
import json

app = Flask(__name__)
CORS(app)

# Initialize database
db = DentalClinicDB()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/patients')
def patients():
    return render_template('patients.html')

@app.route('/appointments')
def appointments():
    return render_template('appointments.html')

@app.route('/treatments')
def treatments():
    return render_template('treatments.html')

@app.route('/reports')
def reports():
    return render_template('reports.html')

# API Routes
@app.route('/api/patients', methods=['GET', 'POST'])
def api_patients():
    if request.method == 'GET':
        patients = db.get_all_patients()
        patients_list = []
        for patient in patients:
            patients_list.append({
                'id': patient[0],
                'name': patient[1],
                'phone': patient[2],
                'email': patient[3],
                'address': patient[4],
                'birth_date': patient[5],
                'medical_history': patient[6],
                'created_at': patient[7]
            })
        return jsonify(patients_list)
    
    elif request.method == 'POST':
        data = request.get_json()
        patient_id = db.add_patient(
            data['name'],
            data.get('phone', ''),
            data.get('email', ''),
            data.get('address', ''),
            data.get('birth_date', ''),
            data.get('medical_history', '')
        )
        return jsonify({'id': patient_id, 'message': 'Patient added successfully'})

@app.route('/api/patients/search')
def api_patients_search():
    search_term = request.args.get('q', '')
    patients = db.search_patients(search_term)
    patients_list = []
    for patient in patients:
        patients_list.append({
            'id': patient[0],
            'name': patient[1],
            'phone': patient[2],
            'email': patient[3],
            'address': patient[4],
            'birth_date': patient[5],
            'medical_history': patient[6],
            'created_at': patient[7]
        })
    return jsonify(patients_list)

@app.route('/api/appointments', methods=['GET', 'POST'])
def api_appointments():
    if request.method == 'GET':
        appointment_date = request.args.get('date', str(date.today()))
        appointments = db.get_appointments_by_date(appointment_date)
        appointments_list = []
        for appointment in appointments:
            appointments_list.append({
                'id': appointment[0],
                'patient_id': appointment[1],
                'appointment_date': appointment[2],
                'appointment_time': appointment[3],
                'treatment_type': appointment[4],
                'status': appointment[5],
                'notes': appointment[6],
                'created_at': appointment[7],
                'patient_name': appointment[8],
                'patient_phone': appointment[9] if len(appointment) > 9 else None
            })
        return jsonify(appointments_list)
    
    elif request.method == 'POST':
        data = request.get_json()
        appointment_id = db.add_appointment(
            data['patient_id'],
            data['appointment_date'],
            data['appointment_time'],
            data['treatment_type'],
            data.get('notes', '')
        )
        return jsonify({'id': appointment_id, 'message': 'Appointment added successfully'})

@app.route('/api/appointments/<int:appointment_id>/status', methods=['PUT'])
def api_update_appointment_status(appointment_id):
    data = request.get_json()
    db.update_appointment_status(appointment_id, data['status'])
    return jsonify({'message': 'Appointment status updated successfully'})

@app.route('/api/treatments', methods=['GET', 'POST'])
def api_treatments():
    if request.method == 'GET':
        patient_id = request.args.get('patient_id')
        if patient_id:
            treatments = db.get_patient_treatments(int(patient_id))
            treatments_list = []
            for treatment in treatments:
                treatments_list.append({
                    'id': treatment[0],
                    'patient_id': treatment[1],
                    'treatment_name': treatment[2],
                    'treatment_date': treatment[3],
                    'cost': treatment[4],
                    'paid_amount': treatment[5],
                    'notes': treatment[6],
                    'created_at': treatment[7]
                })
            return jsonify(treatments_list)
        else:
            return jsonify([])
    
    elif request.method == 'POST':
        data = request.get_json()
        treatment_id = db.add_treatment(
            data['patient_id'],
            data['treatment_name'],
            data['treatment_date'],
            data['cost'],
            data.get('notes', '')
        )
        return jsonify({'id': treatment_id, 'message': 'Treatment added successfully'})

@app.route('/api/reports/daily-revenue')
def api_daily_revenue():
    report_date = request.args.get('date', str(date.today()))
    revenue = db.get_daily_revenue(report_date)
    return jsonify({'revenue': revenue})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
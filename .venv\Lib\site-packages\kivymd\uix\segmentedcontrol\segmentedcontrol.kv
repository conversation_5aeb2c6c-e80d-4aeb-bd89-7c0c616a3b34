#:import SEGMENT_CONTROL_SEGMENT_SWITCH_ELEVATION kivymd.material_resources.SEGMENT_CONTROL_SEGMENT_SWITCH_ELEVATION


<MDSegmentedControlItem>
    adaptive_height: True
    halign: "center"
    pos_hint: {"center_y": .5}
    markup: True


<MDSegmentedControl>
    size_hint: None, None
    size: segment_panel.size

    SegmentSwitch:
        id: segment_switch
        height: segment_panel.height - dp(12)
        pos_hint: {"center_y": .5}
        x: root._segment_switch_x
        md_bg_color: root.segment_color
        elevation: SEGMENT_CONTROL_SEGMENT_SWITCH_ELEVATION
        _radius: root.radius[0] - 4
        shadow_radius: self._radius
        width:
            segment_panel.width / segment_panel.children_number \
            - segment_panel.spacing

    SegmentPanel:
        id: segment_panel
        radius: 12
        spacing: "12dp"
        padding: "12dp"
        size_hint: None, None
        size: "320dp", root.segment_panel_height

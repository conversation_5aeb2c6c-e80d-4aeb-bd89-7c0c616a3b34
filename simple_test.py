import os
from kivy.config import Config

# Force text provider
Config.set('graphics', 'width', '400')
Config.set('graphics', 'height', '600')

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.button import MDRaisedButton
from kivymd.uix.label import MD<PERSON>abel
from kivymd.uix.boxlayout import MDBox<PERSON>ayout
from kivy.metrics import dp
from kivy.core.text import LabelBase

class SimpleTestScreen(MDScreen):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        layout = MDBoxLayout(orientation='vertical', spacing=dp(20), padding=dp(20))
        
        # Test with simple English text
        test_label = MDLabel(
            text="TEST - Can you see this text?",
            font_style="H4",
            halign="center",
            theme_text_color="Primary"
        )
        layout.add_widget(test_label)
        
        # Test button
        test_btn = MDRaisedButton(
            text="CLICK ME",
            size_hint_y=None,
            height=dp(50),
            on_release=self.button_clicked
        )
        layout.add_widget(test_btn)
        
        self.add_widget(layout)
    
    def button_clicked(self, instance):
        print("Button was clicked!")
        instance.text = "CLICKED!"

class SimpleTestApp(MDApp):
    def build(self):
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        return SimpleTestScreen()

if __name__ == '__main__':
    SimpleTestApp().run()

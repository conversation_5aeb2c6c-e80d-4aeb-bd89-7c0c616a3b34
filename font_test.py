from kivy.config import Config
Config.set('graphics', 'width', '400')
Config.set('graphics', 'height', '600')

from kivymd.app import MDApp
from kivymd.uix.screen import MDScreen
from kivymd.uix.label import MD<PERSON>abel
from kivymd.uix.boxlayout import MDBoxLayout
from kivy.metrics import dp
from kivy.core.text import LabelBase
import os

class FontTestScreen(MDScreen):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # Register a specific font
        try:
            LabelBase.register(name="TestFont", 
                             fn_regular="C:/Windows/Fonts/arial.ttf")
            font_name = "TestFont"
        except:
            font_name = None
        
        layout = MDBoxLayout(orientation='vertical', spacing=dp(20), padding=dp(20))
        
        # Test with default font
        label1 = MDLabel(
            text="DEFAULT FONT - Hello World",
            font_style="H5",
            halign="center",
            theme_text_color="Primary"
        )
        layout.add_widget(label1)
        
        # Test with specific font
        if font_name:
            label2 = MDLabel(
                text="ARIAL FONT - Hello World",
                font_name=font_name,
                font_size="20sp",
                halign="center",
                theme_text_color="Secondary"
            )
            layout.add_widget(label2)
        
        # Test Arabic with specific font
        if font_name:
            label3 = MDLabel(
                text="مرحبا بالعالم",
                font_name=font_name,
                font_size="20sp",
                halign="center",
                theme_text_color="Primary"
            )
            layout.add_widget(label3)
        
        self.add_widget(layout)

class FontTestApp(MDApp):
    def build(self):
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        return FontTestScreen()

if __name__ == '__main__':
    FontTestApp().run()

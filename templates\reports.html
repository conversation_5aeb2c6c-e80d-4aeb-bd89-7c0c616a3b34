{% extends "base.html" %}

{% block title %}التقارير - نظام إدارة عيادة الأسنان{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="text-center mb-4">
            <h2 class="text-white fw-bold">
                <i class="bi bi-graph-up"></i>
                التقارير والإحصائيات
            </h2>
            <p class="text-white-50">تقارير شاملة عن أداء العيادة والإيرادات</p>
        </div>
    </div>
</div>

<!-- Date Range Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <label for="startDate" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="startDate">
                    </div>
                    <div class="col-md-3">
                        <label for="endDate" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="endDate">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-primary w-100" onclick="generateReports()">
                            <i class="bi bi-bar-chart"></i>
                            إنشاء التقارير
                        </button>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <button class="btn btn-info w-100" onclick="loadMonthlyReport()">
                            <i class="bi bi-calendar-month"></i>
                            تقرير الشهر
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row g-4 mb-4" id="summary-cards">
    <div class="col-lg-3 col-md-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-primary mb-2">
                    <i class="bi bi-people-fill" style="font-size: 2rem;"></i>
                </div>
                <h4 class="text-primary" id="total-patients-report">0</h4>
                <p class="text-muted mb-0">إجمالي المرضى</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-success mb-2">
                    <i class="bi bi-calendar-check-fill" style="font-size: 2rem;"></i>
                </div>
                <h4 class="text-success" id="total-appointments">0</h4>
                <p class="text-muted mb-0">إجمالي المواعيد</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-info mb-2">
                    <i class="bi bi-bandaid-fill" style="font-size: 2rem;"></i>
                </div>
                <h4 class="text-info" id="total-treatments">0</h4>
                <p class="text-muted mb-0">إجمالي العلاجات</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6">
        <div class="card text-center">
            <div class="card-body">
                <div class="text-warning mb-2">
                    <i class="bi bi-cash-stack" style="font-size: 2rem;"></i>
                </div>
                <h4 class="text-warning" id="total-revenue">0 ر.س</h4>
                <p class="text-muted mb-0">إجمالي الإيرادات</p>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row g-4 mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-pie-chart"></i>
                    توزيع أنواع العلاجات
                </h5>
            </div>
            <div class="card-body">
                <canvas id="treatmentTypesChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    الإيرادات اليومية
                </h5>
            </div>
            <div class="card-body">
                <canvas id="dailyRevenueChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Detailed Reports -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-check"></i>
                    أكثر العلاجات شيوعاً
                </h5>
            </div>
            <div class="card-body">
                <div id="popular-treatments">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-star-fill"></i>
                    أفضل المرضى (حسب قيمة العلاجات)
                </h5>
            </div>
            <div class="card-body">
                <div id="top-patients">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block extra_js %}
<script>
let treatmentTypesChart = null;
let dailyRevenueChart = null;

$(document).ready(function() {
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    
    $('#startDate').val(firstDay.toISOString().split('T')[0]);
    $('#endDate').val(lastDay.toISOString().split('T')[0]);
    
    loadMonthlyReport();
});

function loadMonthlyReport() {
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    
    $('#startDate').val(firstDay.toISOString().split('T')[0]);
    $('#endDate').val(lastDay.toISOString().split('T')[0]);
    
    generateReports();
}

function generateReports() {
    const startDate = $('#startDate').val();
    const endDate = $('#endDate').val();
    
    if (!startDate || !endDate) {
        alert('يرجى اختيار نطاق التاريخ');
        return;
    }
    
    if (new Date(startDate) > new Date(endDate)) {
        alert('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
        return;
    }
    
    loadSummaryData(startDate, endDate);
    loadPopularTreatments(startDate, endDate);
    loadTopPatients(startDate, endDate);
}

function loadSummaryData(startDate, endDate) {
    $.get('/api/patients', function(patients) {
        $('#total-patients-report').text(patients.length);
        
        let allTreatments = [];
        let completedRequests = 0;
        
        if (patients.length === 0) {
            $('#total-treatments').text('0');
            $('#total-revenue').text('0 ر.س');
            return;
        }
        
        patients.forEach(function(patient) {
            $.get('/api/treatments?patient_id=' + patient.id, function(treatments) {
                const filteredTreatments = treatments.filter(function(treatment) {
                    const treatmentDate = new Date(treatment.treatment_date);
                    return treatmentDate >= new Date(startDate) && treatmentDate <= new Date(endDate);
                });
                
                allTreatments = allTreatments.concat(filteredTreatments);
                
                completedRequests++;
                if (completedRequests === patients.length) {
                    $('#total-treatments').text(allTreatments.length);
                    
                    const totalRevenue = allTreatments.reduce((sum, t) => sum + t.paid_amount, 0);
                    $('#total-revenue').text(totalRevenue.toFixed(2) + ' ر.س');
                }
            });
        });
    });
    
    const today = new Date().toISOString().split('T')[0];
    $.get('/api/appointments?date=' + today, function(appointments) {
        $('#total-appointments').text(appointments.length + ' (اليوم)');
    });
}

function loadPopularTreatments(startDate, endDate) {
    $.get('/api/patients', function(patients) {
        let allTreatments = [];
        let completedRequests = 0;
        
        if (patients.length === 0) {
            $('#popular-treatments').html('<p class="text-muted">لا توجد بيانات</p>');
            return;
        }
        
        patients.forEach(function(patient) {
            $.get('/api/treatments?patient_id=' + patient.id, function(treatments) {
                const filteredTreatments = treatments.filter(function(treatment) {
                    const treatmentDate = new Date(treatment.treatment_date);
                    return treatmentDate >= new Date(startDate) && treatmentDate <= new Date(endDate);
                });
                
                allTreatments = allTreatments.concat(filteredTreatments);
                
                completedRequests++;
                if (completedRequests === patients.length) {
                    displayPopularTreatments(allTreatments);
                }
            });
        });
    });
}

function displayPopularTreatments(treatments) {
    const treatmentCounts = {};
    
    treatments.forEach(function(treatment) {
        treatmentCounts[treatment.treatment_name] = (treatmentCounts[treatment.treatment_name] || 0) + 1;
    });
    
    const sortedTreatments = Object.entries(treatmentCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5);
    
    let html = '';
    if (sortedTreatments.length === 0) {
        html = '<p class="text-muted">لا توجد بيانات</p>';
    } else {
        html = '<div class="list-group list-group-flush">';
        sortedTreatments.forEach(function([treatment, count], index) {
            html += '<div class="list-group-item d-flex justify-content-between align-items-center">';
            html += '<span><strong>' + (index + 1) + '.</strong> ' + treatment + '</span>';
            html += '<span class="badge bg-primary rounded-pill">' + count + '</span>';
            html += '</div>';
        });
        html += '</div>';
    }
    
    $('#popular-treatments').html(html);
}

function loadTopPatients(startDate, endDate) {
    $.get('/api/patients', function(patients) {
        let patientRevenue = {};
        let completedRequests = 0;
        
        if (patients.length === 0) {
            $('#top-patients').html('<p class="text-muted">لا توجد بيانات</p>');
            return;
        }
        
        patients.forEach(function(patient) {
            $.get('/api/treatments?patient_id=' + patient.id, function(treatments) {
                const filteredTreatments = treatments.filter(function(treatment) {
                    const treatmentDate = new Date(treatment.treatment_date);
                    return treatmentDate >= new Date(startDate) && treatmentDate <= new Date(endDate);
                });
                
                const totalRevenue = filteredTreatments.reduce((sum, t) => sum + t.paid_amount, 0);
                if (totalRevenue > 0) {
                    patientRevenue[patient.name] = totalRevenue;
                }
                
                completedRequests++;
                if (completedRequests === patients.length) {
                    displayTopPatients(patientRevenue);
                }
            });
        });
    });
}

function displayTopPatients(patientRevenue) {
    const sortedPatients = Object.entries(patientRevenue)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5);
    
    let html = '';
    if (sortedPatients.length === 0) {
        html = '<p class="text-muted">لا توجد بيانات</p>';
    } else {
        html = '<div class="list-group list-group-flush">';
        sortedPatients.forEach(function([patient, revenue], index) {
            html += '<div class="list-group-item d-flex justify-content-between align-items-center">';
            html += '<span><strong>' + (index + 1) + '.</strong> ' + patient + '</span>';
            html += '<span class="badge bg-success rounded-pill">' + revenue.toFixed(2) + ' ر.س</span>';
            html += '</div>';
        });
        html += '</div>';
    }
    
    $('#top-patients').html(html);
}
</script>
{% endblock %}
from kivymd.app import <PERSON><PERSON>pp
from kivymd.uix.screen import MDScreen
from kivymd.uix.screenmanager import MDScreenManager
from kivymd.uix.button import MDRaisedButton, MDIconButton, MDFlatButton
from kivymd.uix.label import <PERSON><PERSON>abel
from kivymd.uix.textfield import MD<PERSON>ext<PERSON>ield
from kivymd.uix.card import MDCard
from kivymd.uix.boxlayout import MDBoxLayout
from kivymd.uix.gridlayout import MDGridLayout
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.list import MDList, OneLineListItem, TwoLineListItem, ThreeLineListItem
from kivymd.uix.dialog import MDDialog
from kivymd.uix.scrollview import MDScrollView
from kivymd.uix.navigationdrawer import MDNavigationDrawer, MDNavigationDrawerMenu
from kivymd.uix.navigationdrawer import MDNavigationDrawerItem
from kivy.metrics import dp
from kivy.lang import Builder
from kivy.clock import Clock
from kivy.core.window import Window
import threading
import requests
import json
from datetime import datetime, date
import subprocess
import sys
import os

# Set window size for desktop testingدد
Window.size = (400, 700)

# Configure for Arabic text support
from kivy.config import Config
Config.set('graphics', 'width', '400')
Config.set('graphics', 'height', '700')

# Set text direction for Arabic
import os
os.environ['KIVY_TEXT_ENCODING'] = 'utf-8'

# Arabic text processing
try:
    import arabic_reshaper
    from bidi.algorithm import get_display

    def process_arabic_text(text):
        """Process Arabic text for proper display"""
        if not text:
            return text
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            bidi_text = get_display(reshaped_text)
            return bidi_text
        except:
            return text
except ImportError:
    def process_arabic_text(text):
        """Fallback function if arabic processing libraries are not available"""
        return text

# Load the kv file for the PatientsScreen
# Builder.load_file('patientsscreen.kv')  # Commented out - file doesn't exist

# Load Arabic styling
try:
    Builder.load_file('arabic_style.kv')
except:
    pass

class MainScreen(MDScreen):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'main'
        
        # Main layout
        main_layout = MDBoxLayout(orientation='vertical', spacing=dp(10), padding=dp(20))
        
        # Top App Bar
        toolbar = MDTopAppBar(
            title=process_arabic_text("عيادة الأسنان الذكية"),
            elevation=3,
            left_action_items=[["menu", lambda x: self.open_drawer()]],
            right_action_items=[["web", lambda x: self.open_web_interface()]],
            pos_hint={"top": 1}
        )
        main_layout.add_widget(toolbar)
        
        # Welcome Card
        welcome_card = MDCard(
            MDBoxLayout(
                MDLabel(
                    text=process_arabic_text("مرحباً بك في نظام إدارة عيادة الأسنان الذكية"),
                    theme_text_color="Primary",
                    font_style="H6",
                    halign="center",
                    size_hint_y=None,
                    height=dp(80),
                    text_size=(350, None),
                    markup=True
                ),
                orientation='vertical',
                padding=dp(20),
                spacing=dp(10)
            ),
            elevation=3,
            size_hint_y=None,
            height=dp(120),
            md_bg_color=[0.2, 0.6, 1, 1]
        )
        main_layout.add_widget(welcome_card)
        
        # Quick Actions Grid
        actions_grid = MDGridLayout(cols=2, spacing=dp(15), size_hint_y=None, height=dp(300))
        
        # Patients Button
        patients_btn = MDRaisedButton(
            text=process_arabic_text("إدارة المرضى"),
            icon="account-group",
            size_hint_y=None,
            height=dp(120),
            md_bg_color=[0.2, 0.8, 0.2, 1],
            on_release=lambda x: self.go_to_screen('patients')
        )
        actions_grid.add_widget(patients_btn)
        
        # Appointments Button
        appointments_btn = MDRaisedButton(
            text=process_arabic_text("إدارة المواعيد"),
            icon="calendar-check",
            size_hint_y=None,
            height=dp(120),
            md_bg_color=[0.8, 0.4, 0.2, 1],
            on_release=lambda x: self.go_to_screen('appointments')
        )
        actions_grid.add_widget(appointments_btn)
        
        # Treatments Button
        treatments_btn = MDRaisedButton(
            text=process_arabic_text("إدارة العلاجات"),
            icon="medical-bag",
            size_hint_y=None,
            height=dp(120),
            md_bg_color=[0.6, 0.2, 0.8, 1],
            on_release=lambda x: self.go_to_screen('treatments')
        )
        actions_grid.add_widget(treatments_btn)
        
        # Reports Button
        reports_btn = MDRaisedButton(
            text=process_arabic_text("التقارير"),
            icon="chart-line",
            size_hint_y=None,
            height=dp(120),
            md_bg_color=[0.8, 0.6, 0.2, 1],
            on_release=lambda x: self.go_to_screen('reports')
        )
        actions_grid.add_widget(reports_btn)
        
        main_layout.add_widget(actions_grid)
        
        # Quick Stats Card
        self.stats_card = MDCard(
            MDBoxLayout(
                MDLabel(
                    text="إحصائيات سريعة",
                    theme_text_color="Primary",
                    font_style="H6",
                    size_hint_y=None,
                    height=dp(40)
                ),
                MDGridLayout(
                    MDLabel(text="المرضى: 0", halign="center", id="patients_count"),
                    MDLabel(text="المواعيد اليوم: 0", halign="center", id="appointments_count"),
                    MDLabel(text="العلاجات: 0", halign="center", id="treatments_count"),
                    MDLabel(text="الإيرادات: 0 ر.س", halign="center", id="revenue_count"),
                    cols=2,
                    spacing=dp(10)
                ),
                orientation='vertical',
                padding=dp(20),
                spacing=dp(10)
            ),
            elevation=2,
            size_hint_y=None,
            height=dp(150)
        )
        main_layout.add_widget(self.stats_card)
        
        self.add_widget(main_layout)
        
        # Load stats
        Clock.schedule_once(lambda dt: self.load_stats(), 1)
    
    def open_drawer(self):
        # Navigation drawer functionality
        pass
    
    def open_web_interface(self):
        # Open web interface in browser
        try:
            import webbrowser
            webbrowser.open('http://localhost:5000')
        except:
            pass
    
    def go_to_screen(self, screen_name):
        self.manager.current = screen_name
    
    def load_stats(self):
        # This would load real stats from the database
        pass

class PatientsScreen(MDScreen):
    # The UI for this screen is now in patientsscreen.kv
    # We only keep the logic here.

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'patients'  # Add screen name
        # Initialize data store. In a real app, this would be empty
        # and populated from the API.
        self.patients_data = []
        # The dialog is not part of the main screen UI, so we can keep it here.
        self.dialog = None

        # Create UI
        main_layout = MDBoxLayout(orientation='vertical', spacing=dp(10), padding=dp(20))

        # Top App Bar
        toolbar = MDTopAppBar(
            title="إدارة المرضى",
            elevation=3,
            left_action_items=[["arrow-right", lambda x: self.go_back()]],
            right_action_items=[["plus", lambda x: self.add_patient()]]
        )
        main_layout.add_widget(toolbar)

        # Search field
        self.search_field = MDTextField(
            hint_text="البحث عن مريض...",
            on_text=self.search_patients
        )
        main_layout.add_widget(self.search_field)

        # Patients list
        self.patients_list = MDList()
        scroll = MDScrollView()
        scroll.add_widget(self.patients_list)
        main_layout.add_widget(scroll)

        self.add_widget(main_layout)

    def on_enter(self, *args):
        """Called when the screen is about to be shown."""
        # Using on_enter is better than Clock.schedule_once in __init__
        # because it ensures the widgets are ready and it reloads
        # data if the user navigates back to the screen.
        self.load_patients()

    def go_back(self):
        self.manager.current = 'main'
    
    def add_patient(self):
        # Show add patient dialog
        # This logic remains the same as it creates a temporary dialog
        content = MDBoxLayout(orientation='vertical', spacing=dp(10), size_hint_y=None, height=dp(400))
        
        self.name_field = MDTextField(hint_text="اسم المريض")
        self.phone_field = MDTextField(hint_text="رقم الهاتف")
        self.email_field = MDTextField(hint_text="البريد الإلكتروني")
        self.address_field = MDTextField(hint_text="العنوان")

        content.add_widget(self.name_field)
        content.add_widget(self.phone_field)
        content.add_widget(self.email_field)
        content.add_widget(self.address_field)
        
        # Check if a dialog is already open to avoid creating multiple instances
        if not self.dialog:
            self.dialog = MDDialog(
                title="إضافة مريض جديد",
                type="custom",
                content_cls=content,
                buttons=[
                    MDFlatButton(
                        text="إلغاء",
                        on_release=self.close_dialog
                    ),
                    MDRaisedButton(
                        text="حفظ",
                        on_release=self.save_patient
                    )
                ]
            )
        self.dialog.open()

    def close_dialog(self, *args):
        if self.dialog:
            self.dialog.dismiss()
            self.dialog = None # Important to allow re-creation

    def save_patient(self, *args):
        if not self.name_field.text.strip():
            return

        # In a real app, this would be a POST request to the API
        patient = {
            'id': len(self.patients_data) + 1,
            'name': self.name_field.text,
            'phone': self.phone_field.text,
            'email': self.email_field.text,
            'address': self.address_field.text,
            'created_at': datetime.now().strftime('%Y-%m-%d')
        }

        self.patients_data.append(patient)
        self.close_dialog()
        self.load_patients() # Refresh the list

    def search_patients(self, instance, text):
        # Use the search field directly
        search_term = text.strip().lower()
        if search_term:
            filtered_patients = [p for p in self.patients_data
                               if search_term in p['name'].lower() or
                                  search_term in p.get('phone', '').lower()]
            self.display_patients(filtered_patients)
        else:
            # If search is cleared, show all patients
            self.load_patients()

    def load_patients(self):
        # In a real app, this would fetch from 'http://.../api/patients'
        # For now, we just display the local data.
        self.display_patients(self.patients_data)

    def display_patients(self, patients):
        # Use the patients_list directly
        self.patients_list.clear_widgets()
        for patient in patients:
            item = TwoLineListItem(
                text=patient['name'],
                secondary_text=f"الهاتف: {patient.get('phone', 'غير محدد')}",
                on_release=lambda x, p=patient: self.view_patient(p)
            )
            self.patients_list.add_widget(item)

    def view_patient(self, patient):
        # Show patient details
        content = MDLabel(
            text=f"الاسم: {patient['name']}\nالهاتف: {patient.get('phone', 'غير محدد')}\nالبريد: {patient.get('email', 'غير محدد')}\nالعنوان: {patient.get('address', 'غير محدد')}",
            halign="right"
        )
        
        dialog = MDDialog(
            title="تفاصيل المريض",
            type="custom",
            content_cls=content,
            buttons=[
                MDRaisedButton(
                    text="إغلاق",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

class AppointmentsScreen(MDScreen):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'appointments'
        self.appointments_data = []
        
        main_layout = MDBoxLayout(orientation='vertical', spacing=dp(10), padding=dp(20))
        
        # Top App Bar
        toolbar = MDTopAppBar(
            title="إدارة المواعيد",
            elevation=3,
            left_action_items=[["arrow-right", lambda x: self.go_back()]],
            right_action_items=[["plus", lambda x: self.add_appointment()]]
        )
        main_layout.add_widget(toolbar)
        
        # Date selector
        date_layout = MDBoxLayout(orientation='horizontal', spacing=dp(10), size_hint_y=None, height=dp(60))
        self.date_field = MDTextField(
            hint_text="التاريخ (YYYY-MM-DD)",
            text=str(date.today()),
            size_hint_x=0.8
        )
        load_btn = MDIconButton(
            icon="calendar",
            on_release=self.load_appointments
        )
        date_layout.add_widget(self.date_field)
        date_layout.add_widget(load_btn)
        main_layout.add_widget(date_layout)
        
        # Appointments List
        self.appointments_list = MDList()
        scroll = MDScrollView()
        scroll.add_widget(self.appointments_list)
        main_layout.add_widget(scroll)
        
        self.add_widget(main_layout)
        
        # Load today's appointments
        Clock.schedule_once(lambda dt: self.load_appointments(), 0.5)
    
    def go_back(self):
        self.manager.current = 'main'
    
    def add_appointment(self):
        # Show add appointment dialog
        content = MDBoxLayout(orientation='vertical', spacing=dp(10), size_hint_y=None, height=dp(350))
        
        self.patient_field = MDTextField(hint_text="اسم المريض")
        self.date_input = MDTextField(hint_text="التاريخ (YYYY-MM-DD)", text=str(date.today()))
        self.time_field = MDTextField(hint_text="الوقت (HH:MM)")
        self.treatment_field = MDTextField(hint_text="نوع العلاج")
        
        content.add_widget(self.patient_field)
        content.add_widget(self.date_input)
        content.add_widget(self.time_field)
        content.add_widget(self.treatment_field)
        
        self.dialog = MDDialog(
            title="حجز موعد جديد",
            type="custom",
            content_cls=content,
            buttons=[
                MDFlatButton(
                    text="إلغاء",
                    on_release=lambda x: self.dialog.dismiss()
                ),
                MDRaisedButton(
                    text="حجز",
                    on_release=self.save_appointment
                )
            ]
        )
        self.dialog.open()
    
    def save_appointment(self, *args):
        if not all([self.patient_field.text, self.date_input.text, 
                   self.time_field.text, self.treatment_field.text]):
            return
        
        appointment = {
            'id': len(self.appointments_data) + 1,
            'patient_name': self.patient_field.text,
            'appointment_date': self.date_input.text,
            'appointment_time': self.time_field.text,
            'treatment_type': self.treatment_field.text,
            'status': 'مجدول'
        }
        
        self.appointments_data.append(appointment)
        self.dialog.dismiss()
        self.load_appointments()
    
    def load_appointments(self, *args):
        selected_date = self.date_field.text or str(date.today())
        filtered_appointments = [a for a in self.appointments_data 
                               if a['appointment_date'] == selected_date]
        self.display_appointments(filtered_appointments)
    
    def display_appointments(self, appointments):
        self.appointments_list.clear_widgets()
        for appointment in appointments:
            item = ThreeLineListItem(
                text=f"{appointment['appointment_time']} - {appointment['patient_name']}",
                secondary_text=f"العلاج: {appointment['treatment_type']}",
                tertiary_text=f"الحالة: {appointment['status']}",
                on_release=lambda x, a=appointment: self.view_appointment(a)
            )
            self.appointments_list.add_widget(item)
    
    def view_appointment(self, appointment):
        content = MDLabel(
            text=f"المريض: {appointment['patient_name']}\nالوقت: {appointment['appointment_time']}\nالعلاج: {appointment['treatment_type']}\nالحالة: {appointment['status']}",
            halign="right"
        )
        
        dialog = MDDialog(
            title="تفاصيل الموعد",
            type="custom",
            content_cls=content,
            buttons=[
                MDRaisedButton(
                    text="إغلاق",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

class TreatmentsScreen(MDScreen):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'treatments'
        self.treatments_data = []
        
        main_layout = MDBoxLayout(orientation='vertical', spacing=dp(10), padding=dp(20))
        
        # Top App Bar
        toolbar = MDTopAppBar(
            title="إدارة العلاجات",
            elevation=3,
            left_action_items=[["arrow-right", lambda x: self.go_back()]],
            right_action_items=[["plus", lambda x: self.add_treatment()]]
        )
        main_layout.add_widget(toolbar)
        
        # Treatments List
        self.treatments_list = MDList()
        scroll = MDScrollView()
        scroll.add_widget(self.treatments_list)
        main_layout.add_widget(scroll)
        
        self.add_widget(main_layout)
        
        Clock.schedule_once(lambda dt: self.load_treatments(), 0.5)
    
    def go_back(self):
        self.manager.current = 'main'
    
    def add_treatment(self):
        # Show add treatment dialog
        content = MDBoxLayout(orientation='vertical', spacing=dp(10), size_hint_y=None, height=dp(400))
        
        self.patient_field = MDTextField(hint_text="اسم المريض")
        self.treatment_field = MDTextField(hint_text="نوع العلاج")
        self.date_field = MDTextField(hint_text="التاريخ (YYYY-MM-DD)", text=str(date.today()))
        self.cost_field = MDTextField(hint_text="التكلفة")
        self.notes_field = MDTextField(hint_text="ملاحظات")
        
        content.add_widget(self.patient_field)
        content.add_widget(self.treatment_field)
        content.add_widget(self.date_field)
        content.add_widget(self.cost_field)
        content.add_widget(self.notes_field)
        
        self.dialog = MDDialog(
            title="إضافة علاج جديد",
            type="custom",
            content_cls=content,
            buttons=[
                MDFlatButton(
                    text="إلغاء",
                    on_release=lambda x: self.dialog.dismiss()
                ),
                MDRaisedButton(
                    text="حفظ",
                    on_release=self.save_treatment
                )
            ]
        )
        self.dialog.open()
    
    def save_treatment(self, *args):
        if not all([self.patient_field.text, self.treatment_field.text, 
                   self.date_field.text, self.cost_field.text]):
            return
        
        treatment = {
            'id': len(self.treatments_data) + 1,
            'patient_name': self.patient_field.text,
            'treatment_name': self.treatment_field.text,
            'treatment_date': self.date_field.text,
            'cost': float(self.cost_field.text) if self.cost_field.text.replace('.', '').isdigit() else 0,
            'paid_amount': 0,
            'notes': self.notes_field.text
        }
        
        self.treatments_data.append(treatment)
        self.dialog.dismiss()
        self.load_treatments()
    
    def load_treatments(self):
        self.treatments_list.clear_widgets()
        for treatment in self.treatments_data:
            remaining = treatment['cost'] - treatment['paid_amount']
            item = ThreeLineListItem(
                text=f"{treatment['patient_name']} - {treatment['treatment_name']}",
                secondary_text=f"التاريخ: {treatment['treatment_date']}",
                tertiary_text=f"التكلفة: {treatment['cost']} ر.س - المتبقي: {remaining} ر.س",
                on_release=lambda x, t=treatment: self.view_treatment(t)
            )
            self.treatments_list.add_widget(item)
    
    def view_treatment(self, treatment):
        remaining = treatment['cost'] - treatment['paid_amount']
        content = MDLabel(
            text=f"المريض: {treatment['patient_name']}\nالعلاج: {treatment['treatment_name']}\nالتاريخ: {treatment['treatment_date']}\nالتكلفة: {treatment['cost']} ر.س\nالمدفوع: {treatment['paid_amount']} ر.س\nالمتبقي: {remaining} ر.س\nملاحظات: {treatment.get('notes', 'لا توجد')}",
            halign="right"
        )
        
        dialog = MDDialog(
            title="تفاصيل العلاج",
            type="custom",
            content_cls=content,
            buttons=[
                MDRaisedButton(
                    text="إغلاق",
                    on_release=lambda x: dialog.dismiss()
                )
            ]
        )
        dialog.open()

class ReportsScreen(MDScreen):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.name = 'reports'
        
        main_layout = MDBoxLayout(orientation='vertical', spacing=dp(10), padding=dp(20))
        
        # Top App Bar
        toolbar = MDTopAppBar(
            title="التقارير",
            elevation=3,
            left_action_items=[["arrow-right", lambda x: self.go_back()]]
        )
        main_layout.add_widget(toolbar)
        
        # Reports content
        reports_card = MDCard(
            MDBoxLayout(
                MDLabel(
                    text="التقارير والإحصائيات",
                    font_style="H5",
                    halign="center",
                    theme_text_color="Primary"
                ),
                MDLabel(
                    text="سيتم عرض التقارير المالية والإحصائيات هنا",
                    halign="center",
                    theme_text_color="Secondary"
                ),
                orientation='vertical',
                padding=dp(20),
                spacing=dp(20)
            ),
            elevation=2,
            padding=dp(20)
        )
        main_layout.add_widget(reports_card)
        
        # Quick stats
        stats_grid = MDGridLayout(cols=2, spacing=dp(10), size_hint_y=None, height=dp(200))
        
        # Total patients card
        patients_card = MDCard(
            MDBoxLayout(
                MDLabel(text="إجمالي المرضى", font_style="Caption", halign="center"),
                MDLabel(text="0", font_style="H4", halign="center", theme_text_color="Primary"),
                orientation='vertical',
                padding=dp(10)
            ),
            elevation=1,
            md_bg_color=[0.9, 0.9, 1, 1]
        )
        stats_grid.add_widget(patients_card)
        
        # Total revenue card
        revenue_card = MDCard(
            MDBoxLayout(
                MDLabel(text="إجمالي الإيرادات", font_style="Caption", halign="center"),
                MDLabel(text="0 ر.س", font_style="H4", halign="center", theme_text_color="Primary"),
                orientation='vertical',
                padding=dp(10)
            ),
            elevation=1,
            md_bg_color=[0.9, 1, 0.9, 1]
        )
        stats_grid.add_widget(revenue_card)
        
        # Pending payments card
        pending_card = MDCard(
            MDBoxLayout(
                MDLabel(text="المدفوعات المعلقة", font_style="Caption", halign="center"),
                MDLabel(text="0 ر.س", font_style="H4", halign="center", theme_text_color="Primary"),
                orientation='vertical',
                padding=dp(10)
            ),
            elevation=1,
            md_bg_color=[1, 0.9, 0.9, 1]
        )
        stats_grid.add_widget(pending_card)
        
        # This month treatments
        treatments_card = MDCard(
            MDBoxLayout(
                MDLabel(text="علاجات هذا الشهر", font_style="Caption", halign="center"),
                MDLabel(text="0", font_style="H4", halign="center", theme_text_color="Primary"),
                orientation='vertical',
                padding=dp(10)
            ),
            elevation=1,
            md_bg_color=[1, 1, 0.9, 1]
        )
        stats_grid.add_widget(treatments_card)
        
        main_layout.add_widget(stats_grid)
        
        self.add_widget(main_layout)
    
    def go_back(self):
        self.manager.current = 'main'

class DentalClinicApp(MDApp):
    def build(self):
        self.theme_cls.theme_style = "Light"
        self.theme_cls.primary_palette = "Blue"
        self.theme_cls.material_style = "M3"

        # Set font for Arabic text support
        from kivy.core.text import LabelBase
        try:
            # Try to register an Arabic font if available
            LabelBase.register(name="Arabic",
                             fn_regular="C:/Windows/Fonts/arial.ttf")
        except:
            # Fallback to default font
            pass
        
        # Create screen manager
        sm = MDScreenManager()
        
        # Add screens
        sm.add_widget(MainScreen())
        sm.add_widget(PatientsScreen())
        sm.add_widget(AppointmentsScreen())
        sm.add_widget(TreatmentsScreen())
        sm.add_widget(ReportsScreen())
        
        # Start web server in background (disabled temporarily)
        # self.start_web_server()
        
        return sm
    
    def start_web_server(self):
        def run_server():
            try:
                # Start the Flask web server
                subprocess.Popen([sys.executable, 'web_server.py'], 
                               cwd=os.path.dirname(os.path.abspath(__file__)))
            except Exception as e:
                print(f"Error starting web server: {e}")
        
        # Start server in a separate thread
        server_thread = threading.Thread(target=run_server)
        server_thread.daemon = True
        server_thread.start()

if __name__ == '__main__':
    DentalClinicApp().run()
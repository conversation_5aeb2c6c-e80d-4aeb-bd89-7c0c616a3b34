<MDBottomSheetContent>
    size_hint_y: None
    height: self.minimum_height


<MDBottomSheetDragHandle>
    orientation: "vertical"
    size_hint_y: None
    height: self.minimum_height
    padding: "16dp", "8dp", "16dp", "16dp"

    BottomSheetDragHandle:
        md_bg_color:
            app.theme_cls.disabled_hint_text_color \
            if not root.drag_handle_color else \
            root.drag_handle_color
        size_hint: None, None
        size: "32dp", "4dp"
        radius: 4
        pos_hint: {"center_x": .5}

    BottomSheetDragHandleContainer:
        id: header_container
        size_hint_y: None
        height: self.minimum_height


<MDBottomSheet>
    orientation: "vertical"
    md_bg_color: root.bg_color if root.bg_color else app.theme_cls.bg_darkest
    radius: 16, 16, 0, 0
    padding: 0, "8dp", 0, 0

    MDBoxLayout:
        id: drag_handle_container
        size_hint_y: None
        height: self.minimum_height

    MDBoxLayout:
        id: container
        size_hint_y: None
        height: self.minimum_height
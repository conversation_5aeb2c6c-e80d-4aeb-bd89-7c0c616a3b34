{% extends "base.html" %}

{% block title %}إدارة المرضى - نظام إدارة عيادة الأسنان{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="text-white fw-bold">
                <i class="bi bi-people-fill"></i>
                إدارة المرضى
            </h2>
            <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addPatientModal">
                <i class="bi bi-plus-circle"></i>
                إضافة مريض جديد
            </button>
        </div>
    </div>
</div>

<!-- Search Bar -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <input type="text" class="form-control" id="searchInput" placeholder="البحث عن مريض (الاسم، الهاتف، البريد الإلكتروني)">
                    </div>
                    <div class="col-md-4">
                        <button class="btn btn-primary w-100" onclick="searchPatients()">
                            <i class="bi bi-search"></i>
                            بحث
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Patients List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul"></i>
                    قائمة المرضى
                </h5>
            </div>
            <div class="card-body">
                <div id="patients-list">
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Patient Modal -->
<div class="modal fade" id="addPatientModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مريض جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addPatientForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="patientName" class="form-label">اسم المريض *</label>
                            <input type="text" class="form-control" id="patientName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="patientPhone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="patientPhone">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="patientEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="patientEmail">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="patientBirthDate" class="form-label">تاريخ الميلاد</label>
                            <input type="date" class="form-control" id="patientBirthDate">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="patientAddress" class="form-label">العنوان</label>
                        <textarea class="form-control" id="patientAddress" rows="2"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="patientMedicalHistory" class="form-label">التاريخ المرضي</label>
                        <textarea class="form-control" id="patientMedicalHistory" rows="3" placeholder="أي أمراض مزمنة، حساسية، أدوية، عمليات سابقة..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="addPatient()">
                    <i class="bi bi-check-circle"></i>
                    حفظ المريض
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    loadPatients();
    
    $('#searchInput').on('keypress', function(e) {
        if (e.which === 13) {
            searchPatients();
        }
    });
});

function loadPatients() {
    $.get('/api/patients', function(data) {
        displayPatients(data);
    }).fail(function() {
        $('#patients-list').html('<div class="alert alert-danger">حدث خطأ في تحميل البيانات</div>');
    });
}

function searchPatients() {
    const searchTerm = $('#searchInput').val().trim();
    
    if (searchTerm === '') {
        loadPatients();
        return;
    }
    
    $.get('/api/patients/search?q=' + encodeURIComponent(searchTerm), function(data) {
        displayPatients(data);
    }).fail(function() {
        $('#patients-list').html('<div class="alert alert-danger">حدث خطأ في البحث</div>');
    });
}

function displayPatients(patients) {
    let html = '';
    
    if (patients.length === 0) {
        html = '<div class="text-center py-4"><p class="text-muted">لا توجد مرضى</p></div>';
    } else {
        html = '<div class="table-responsive"><table class="table table-hover">';
        html += '<thead><tr><th>الاسم</th><th>الهاتف</th><th>البريد الإلكتروني</th><th>تاريخ التسجيل</th><th>الإجراءات</th></tr></thead><tbody>';
        
        patients.forEach(function(patient) {
            const createdDate = new Date(patient.created_at).toLocaleDateString('ar-SA');
            
            html += '<tr>';
            html += '<td><strong>' + patient.name + '</strong></td>';
            html += '<td>' + (patient.phone || '-') + '</td>';
            html += '<td>' + (patient.email || '-') + '</td>';
            html += '<td>' + createdDate + '</td>';
            html += '<td>';
            html += '<button class="btn btn-sm btn-info me-2" onclick="viewPatientDetails(' + patient.id + ')">';
            html += '<i class="bi bi-eye"></i> عرض';
            html += '</button>';
            html += '<button class="btn btn-sm btn-primary" onclick="addAppointment(' + patient.id + ')">';
            html += '<i class="bi bi-calendar-plus"></i> موعد';
            html += '</button>';
            html += '</td>';
            html += '</tr>';
        });
        
        html += '</tbody></table></div>';
    }
    
    $('#patients-list').html(html);
}

function addPatient() {
    const patientData = {
        name: $('#patientName').val().trim(),
        phone: $('#patientPhone').val().trim(),
        email: $('#patientEmail').val().trim(),
        address: $('#patientAddress').val().trim(),
        birth_date: $('#patientBirthDate').val(),
        medical_history: $('#patientMedicalHistory').val().trim()
    };
    
    if (!patientData.name) {
        alert('يرجى إدخال اسم المريض');
        return;
    }
    
    $.ajax({
        url: '/api/patients',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify(patientData),
        success: function(response) {
            $('#addPatientModal').modal('hide');
            $('#addPatientForm')[0].reset();
            loadPatients();
            
            const alert = '<div class="alert alert-success alert-dismissible fade show" role="alert">';
            alert += 'تم إضافة المريض بنجاح!';
            alert += '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>';
            alert += '</div>';
            
            $('.container.main-content').prepend(alert);
            
            setTimeout(function() {
                $('.alert-success').fadeOut();
            }, 3000);
        },
        error: function() {
            alert('حدث خطأ في إضافة المريض');
        }
    });
}

function viewPatientDetails(patientId) {
    alert('عرض تفاصيل المريض رقم: ' + patientId);
}

function addAppointment(patientId) {
    window.location.href = '/appointments?patient_id=' + patientId;
}
</script>
{% endblock %}
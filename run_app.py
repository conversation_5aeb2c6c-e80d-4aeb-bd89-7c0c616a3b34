#!/usr/bin/env python3
"""
تطبيق إدارة عيادة الأسنان
يشغل التطبيق المحمول (Kivy) وخادم الويب (Flask) معاً
"""

import threading
import time
import subprocess
import sys
import os
from pathlib import Path

def start_web_server():
    """تشغيل خادم الويب Flask"""
    print("🌐 بدء تشغيل خادم الويب...")
    try:
        subprocess.run([sys.executable, "web_server.py"], check=True)
    except Exception as e:
        print(f"❌ خطأ في تشغيل خادم الويب: {e}")

def start_mobile_app():
    """تشغيل تطبيق الهاتف المحمول Kivy"""
    print("📱 بدء تشغيل تطبيق الهاتف المحمول...")
    try:
        subprocess.run([sys.executable, "main.py"], check=True)
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق المحمول: {e}")

def main():
    """الدالة الرئيسية لتشغيل النظام"""
    print("🦷 مرحباً بك في نظام إدارة عيادة الأسنان الذكية")
    print("=" * 50)
    
    # التحقق من وجود الملفات المطلوبة
    required_files = ["main.py", "web_server.py", "database.py"]
    missing_files = []
    
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ الملفات التالية مفقودة: {', '.join(missing_files)}")
        return
    
    print("✅ جميع الملفات المطلوبة موجودة")
    
    # عرض خيارات التشغيل
    print("\nاختر طريقة التشغيل:")
    print("1. تشغيل خادم الويب فقط")
    print("2. تشغيل تطبيق الهاتف المحمول فقط")
    print("3. تشغيل كلاهما معاً")
    print("4. خروج")
    
    choice = input("\nأدخل اختيارك (1-4): ").strip()
    
    if choice == "1":
        print("\n🌐 تشغيل خادم الويب...")
        print("يمكنك الوصول للنظام عبر: http://localhost:5000")
        start_web_server()
        
    elif choice == "2":
        print("\n📱 تشغيل تطبيق الهاتف المحمول...")
        start_mobile_app()
        
    elif choice == "3":
        print("\n🚀 تشغيل النظام الكامل...")
        print("خادم الويب: http://localhost:5000")
        
        # تشغيل خادم الويب في خيط منفصل
        web_thread = threading.Thread(target=start_web_server, daemon=True)
        web_thread.start()
        
        # انتظار قليل لبدء الخادم
        time.sleep(2)
        
        # تشغيل التطبيق المحمول
        start_mobile_app()
        
    elif choice == "4":
        print("👋 شكراً لاستخدام نظام إدارة عيادة الأسنان")
        
    else:
        print("❌ اختيار غير صحيح")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ حدث خطأ غير متوقع: {e}")